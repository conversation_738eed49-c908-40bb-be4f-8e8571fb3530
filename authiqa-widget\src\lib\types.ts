import { WidgetCustomization } from './customization-types';

export interface AuthUrls {
  signup: string;
  signin: string;
  verify: string;
  reset: string;
  update: string;
  resend: string;
  successful: string;
}


export interface WidgetConfig {
  publicKey: string;
  container: string;
  mode: 'popup' | 'inline';
  theme?: 'light' | 'dark' | 'none';
  disableStyles?: boolean;
  organizationDomain: string;
  customization?: Partial<WidgetCustomization>;
  enableGoogleOneTap?: boolean; // Control Google One Tap behavior
  termsAndConditions?: string;
  privacy?: string;
  notificationSettings?: string;
  verifyAuthPath?: string;    // Full URL: https://domain.com/verify
  updatePasswordPath?: string; // Full URL: https://domain.com/reset
  resendAuthPath?: string;    // Full URL: https://domain.com/resend
  successAuthPath?: string;   // Full URL: https://domain.com/success
  signinAuthPath?: string;    // Full URL: https://domain.com/signin
  signupAuthPath?: string;    // Full URL: https://domain.com/signup
  resetAuthPath?: string;    // Full URL for password reset page
  messages?: {
    // Success messages
    signinSuccess?: string;
    signupSuccess?: string;
    resetSuccess?: string;
    updateSuccess?: string;
    resendSuccess?: string;
    verificationSuccess?: string;
    
    // Loading messages
    signinLoading?: string;
    signupLoading?: string;
    resetLoading?: string;
    updateLoading?: string;
    resendLoading?: string;
    verificationLoading?: string;
  };
}

export interface ApiError {
    code: string;
    message: string;
}

export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: ApiError;
}

// Success response type
export interface SignupSuccessResponse {
    success: true;
    data: {
        token: string; // JWT token inside data
        data: {
            userID: string;
            username: string;
            email: string;
            createdAt: number;
            publicKey: string;
            emailVerified: boolean;
            parentAccount: string | null;
            accountType: string;
            emailVerificationSent: boolean;
            localDevelopment?: {
                verificationCode: string;
            };
        };
    };
}

// Error response type
export interface SignupErrorResponse {
    success: false;
    error: {
        code: string;
        message: string;
    };
}

// Combined response type
export type SignupResponse = SignupSuccessResponse | SignupErrorResponse;

export interface SigninSuccessResponse {
    success: true;
    data: {
        token: {
            accessToken: string;
            refreshToken: string;
            userId: string;
            username: string;
            accountType: 'parent' | 'child';
            parentAccount: string;
            publicKey: string;
            createdAt: number;
        },
        passwordStatus?: {
            expired: boolean;
            daysUntilExpiry: number;
        }
    };
}

export interface SigninErrorResponse {
    success: false;
    error: {
        code: string;
        message: string;
    };
}

export type SigninResponse = SigninSuccessResponse | SigninErrorResponse;

export interface ResendConfirmationSuccessResponse {
    success: true;
    data: {
        message: string;
    };
}

export interface ResendConfirmationErrorResponse {
    success: false;
    error: {
        code: string;
        message: string;
    };
}

export type ResendConfirmationResponse = ResendConfirmationSuccessResponse | ResendConfirmationErrorResponse;

export interface AuthResponse {
    success: boolean;
    data?: {
        status: string;
        lastChecked: number;
    };
    error?: ApiError;
}

export interface OrganizationDetailsResponse {
  message: string;
  authUrls: AuthUrls;
  organizationUrl: string;
  domainRestrictionEnabled: boolean;
  emailVerificationRequired?: boolean; // New field for email verification requirement
  googleSsoConfig?: {
    enabled: boolean;
    clientId: string;
  };
}

