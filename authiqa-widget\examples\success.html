<!DOCTYPE html>
<html>
<head>
    <title>Success - Welcome to Your Account</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .success-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 48px;
            color: #28a745;
            margin-bottom: 20px;
        }
        .verification-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
        }
        .verification-notice h3 {
            margin-top: 0;
            color: #856404;
        }
        .verification-notice p {
            margin-bottom: 15px;
            color: #856404;
        }
        .dashboard-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 10px;
        }
        .dashboard-link:hover {
            background: #0056b3;
        }
        .test-controls {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            text-align: left;
        }
        .test-controls button {
            margin: 5px;
            padding: 8px 12px;
            border: 1px solid #6c757d;
            background: #6c757d;
            color: white;
            border-radius: 3px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✅</div>
        <h1>Welcome to Your Account!</h1>
        <p>Your account has been created successfully and you can start using our platform right away.</p>
        
        <!-- Test Controls -->
        <div class="test-controls">
            <h4>🧪 Test Controls:</h4>
            <button onclick="simulateLogin()">Simulate Login</button>
            <button onclick="clearLogin()">Clear Login</button>
            <button onclick="refreshPage()">Refresh Page</button>
            <p><strong>Status:</strong> <span id="auth-status">Checking...</span></p>
        </div>
        
        <!-- Email Verification Notice (when verification is optional) -->
        <div class="verification-notice">
            <h3>📧 Email Verification (Optional)</h3>
            <p>While you can use your account immediately, we recommend verifying your email address to:</p>
            <ul>
                <li>Secure your account</li>
                <li>Receive important notifications</li>
                <li>Enable password recovery</li>
            </ul>
            
            <!-- This is where the resend widget will appear -->
            <div id="authiqa"></div>
        </div>

        <a href="#" class="dashboard-link">Go to Dashboard</a>
        <a href="signin.html" class="dashboard-link" style="background: #6c757d;">Sign Out</a>
    </div>

    <!-- Include the Authiqa widget for resend functionality -->
    <script src="global-config.js"></script>
    <script
        src="../dist/index.js"
        defer
        data-public-key="APK_c9a1b79b109c31cbe9db3791e3f58725_1747808986"
        action="resend"
        theme="light"
        verifyAuthPath="verify-email.html"
        signinAuthPath="signin.html"
    ></script>

    <script>
        // Test functions
        function simulateLogin() {
            localStorage.setItem('access_token', 'demo_access_token_12345');
            sessionStorage.setItem('refresh_token', 'demo_refresh_token_12345');
            updateAuthStatus();
            location.reload(); // Reload to see the authenticated resend form
        }

        function clearLogin() {
            localStorage.removeItem('access_token');
            sessionStorage.removeItem('refresh_token');
            localStorage.removeItem('authiqa_last_resend_time');
            updateAuthStatus();
            location.reload(); // Reload to see the traditional resend form
        }

        function refreshPage() {
            location.reload();
        }

        function updateAuthStatus() {
            const hasToken = localStorage.getItem('access_token') || sessionStorage.getItem('refresh_token');
            const statusElement = document.getElementById('auth-status');
            
            if (hasToken) {
                statusElement.textContent = 'Logged In (will show authenticated resend form)';
                statusElement.style.color = '#28a745';
            } else {
                statusElement.textContent = 'Not Logged In (will show traditional resend form)';
                statusElement.style.color = '#dc3545';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate that user is logged in by default (for testing)
            if (!localStorage.getItem('access_token') && !sessionStorage.getItem('refresh_token')) {
                simulateLogin();
            }
            updateAuthStatus();
        });
    </script>
</body>
</html>
